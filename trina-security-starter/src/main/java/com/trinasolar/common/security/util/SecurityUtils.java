package com.trinasolar.common.security.util;

import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.domain.IamUserInfo;
import com.trinasolar.common.security.domain.SecurityContext;
import com.trinasolar.common.security.domain.TaspPermissionInfo;
import com.trinasolar.common.security.domain.TaspRoleInfo;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.util.PathMatcher;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 安全工具类
 * <p>
 * 提供便捷的权限检查和用户信息获取方法
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@UtilityClass
public class SecurityUtils {

    private static final PathMatcher PATH_MATCHER = new AntPathMatcher();

    public final static String HEADER_USER_ID = "X-User-Id";
    /**
     * 默认白名单，注意为包含contextPath的路径
     */
    public static final List<String> DEFAULT_WHITE_URLS = List.of(
            "/api/scf/token",
            "/swagger-ui/**", "/swagger-resources/**", "/webjars/**", "/v3/api-docs/**", "/v2/api-docs/**",
            "favicon.ico", "/error", "/actuator/**", "health"
    );

    /**
     * 获取当前安全上下文
     */
    public SecurityContext getCurrentContext() {
        return SecurityContextHolder.getContext();
    }

    /**
     * 获取当前用户ID
     */
    public String getCurrentUserId() {
        return SecurityContextHolder.getCurrentUserId();
    }

    /**
     * 获取当前用户名
     */
    public String getCurrentUsername() {
        return SecurityContextHolder.getCurrentUsername();
    }

    /**
     * 获取当前用户角色
     */
    public List<String> getCurrentUserRoleCodes() {
        return SecurityContextHolder.getCurrentUserRoleCodes();
    }

    /**
     * 获取当前用户权限
     */
    public List<String> getCurrentUserPermissions() {
        return SecurityContextHolder.getCurrentUserPermissions();
    }

    /**
     * 获取当前用户数据权限范围
     */
    public List<TaspRoleInfo> getCurrentUserRoles() {
        return SecurityContextHolder.getCurrentUserRoles();
    }

    /**
     * 检查当前用户是否拥有指定角色
     */
    public boolean hasRole(String role) {
        return SecurityContextHolder.hasRole(role);
    }

    /**
     * 检查当前用户是否拥有任意一个角色
     */
    public boolean hasAnyRole(String... roles) {
        return SecurityContextHolder.hasAnyRole(roles);
    }

    /**
     * 检查当前用户是否拥有指定权限
     */
    public boolean hasPermission(String permission) {
        return SecurityContextHolder.hasPermission(permission);
    }

    /**
     * 检查当前用户是否拥有任意一个权限
     */
    public boolean hasAnyPermission(String... permissions) {
        return SecurityContextHolder.hasAnyPermission(permissions);
    }

    /**
     * 检查当前用户是否为管理员
     */
    public boolean isAdmin() {
        return hasRole("admin");
    }

    /**
     * 检查当前用户是否已登录
     */
    public boolean isAuthenticated() {
        return SecurityContextHolder.isAuthenticated();
    }

    /**
     * 检查当前用户是否为超级管理员
     */
    public boolean isSuperAdmin() {
        return hasRole("super_admin");
    }

    /**
     * 获取当前用户的邮箱
     */
    public String getCurrentUserEmail() {
        SecurityContext context = getCurrentContext();
        return context != null ? context.getEmail() : null;
    }

    /**
     * 获取当前用户的显示名称
     */
    public String getCurrentUserDisplayName() {
        return SecurityContextHolder.getCurrentDisplayName();
    }

    /**
     * 匹配白名单URL
     */
    public boolean matchWhiteUrl(List<String> whiteUrls, String path, String contextPath) {
        if (CollectionUtils.isEmpty(whiteUrls)) {
            return false;
        }

        for (String whiteUrl : whiteUrls) {
            String pattern = StringUtils.hasText(contextPath) ? contextPath + whiteUrl : whiteUrl;
            if (PATH_MATCHER.match(pattern, path)) {
                log.debug("Request {} matched white URL pattern: {}", path, pattern);
                return true;
            }
        }
        return false;
    }

    // 通用白名单匹配方法
    public static boolean matchWhiteUrl(List<String> whiteUrls, String path) {
        log.debug("Matching path: {} against whitelist: {}", path, whiteUrls);
        if (whiteUrls == null || whiteUrls.isEmpty()) {
            return false;
        }
        return whiteUrls.stream()
                .anyMatch(pattern -> {
                    boolean match = PATH_MATCHER.match(pattern, path);
                    if (match) {
                        log.debug("Path {} matched whitelist pattern: {}", path, pattern);
                    }
                    return match;
                });
    }

    public void setSecurityContext(String token, IamUserInfo userInfo, TaspPermissionInfo permissionInfo) {
        try {
            // 构建安全上下文
            SecurityContext securityContext = buildSecurityContext(token, userInfo, permissionInfo);
            // 设置到ThreadLocal
            SecurityContextHolder.setContext(securityContext);

            log.debug("Security context set from cache for user: {}", userInfo.getUid());
        } catch (Exception e) {
            log.error("Error setting security context from cache", e);
        }
    }


    /**
     * 构建安全上下文（从IamUserInfo）
     */
    public SecurityContext buildSecurityContext(String token, IamUserInfo userInfo, TaspPermissionInfo permissionInfo) {
        SecurityContext context = new SecurityContext();

        // 设置认证token
        context.setAccessToken(token);
        context.setAuthenticated(true);
        context.setAuthenticatedAt(LocalDateTime.now());

        // 设置用户主体信息
        context.setUserId(userInfo.getUid());
        context.setUsername(userInfo.getUid());
        context.setDisplayName(userInfo.getDisplayname());
        context.setEmail(userInfo.getEmail());
        context.setDeptId(userInfo.getDepartmentnumber());
        context.setEmployeeNumber(userInfo.getEmployeenumber());
        context.setPostType(userInfo.getPosttype());
        context.setLocation(userInfo.getLocation());
        context.setFilled(userInfo.getCn());

        // 设置权限信息
        context.setRoleCodes(permissionInfo.getRoleCodes());
        context.setPermissions(permissionInfo.getPermissions());
        permissionInfo.setRoles(permissionInfo.getRoles());
        return context;
    }

    /**
     * 从请求参数或请求头中获取指定key的值（优先级：参数 > 头信息）
     * @param request 请求对象
     * @param key 要获取的键名
     * @return 匹配到的第一个值，未找到返回null
     */
    public static String getValueFromRequest(ServerHttpRequest request, String key) {
        // 优先从查询参数获取
        String paramValue = request.getQueryParams().getFirst(key);
        if (StringUtils.hasText(paramValue)) {
            return paramValue;
        }
        // 从请求头获取
        String headerValue = request.getHeaders().getFirst(key);
        if (StringUtils.hasText(headerValue)) {
            return headerValue;
        }

        return null;
    }

    /**
     * 构建有Bearer前缀令牌
     *
     * @param token token
     * @return {@link String }
     */
    public String buildAuthorization(String token) {
        if (StringUtils.hasText(token) && !token.startsWith(SecurityConstant.TOKEN_PREFIX)) {
            return SecurityConstant.TOKEN_PREFIX + token;
        }
        return token;
    }

    /**
     * 构建无Bearer前缀令牌
     *
     * @param token token
     * @return {@link String }
     */
    public String buildToken(String token) {
        if (StringUtils.hasText(token) && token.startsWith(SecurityConstant.TOKEN_PREFIX)) {
            return token.substring(SecurityConstant.TOKEN_PREFIX.length()).trim();
        }
        return token;
    }
}
