package com.trinasolar.common.security.domain;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 统一权限平台信息
 *
 * <AUTHOR>
 * @since 2025-07-31 09:49
 **/
@Data
public class TaspPermissionInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 8931121532506553410L;

    @Schema(description = "用户角色列表", example = "[\"ADMIN\", \"USER\"]")
    private List<String> roleCodes;

    @Schema(description = "权限列表", example = "[\"user:read\", \"user:write\"]")
    private List<String> permissions;

    @Schema(description = "数据权限范围")
    private List<TaspRoleInfo> roles;
}
