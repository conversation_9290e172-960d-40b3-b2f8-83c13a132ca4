package com.trinasolar.common.security.endpoint;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.trinasolar.common.core.base.R;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.domain.IamTokenInfo;
import com.trinasolar.common.security.domain.IamUserInfo;
import com.trinasolar.common.security.domain.SecurityContext;
import com.trinasolar.common.security.service.RemoteInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * auth 端点
 *
 * <AUTHOR>
 * @date 2025-06-20 14:02
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/scf")
@Tag(name = "认证管理", description = "IAM认证相关接口")
public class AuthEndpoint {

    private final RemoteInfoService remoteInfoService;

    /**
     * 通过授权码从 TAM 远程获取令牌
     *
     * @param code 授权码
     * @return {@link R }<{@link IamTokenInfo }>
     */
    @Operation(
            summary = "获取访问令牌",
            description = "通过授权码模式获取IAM访问令牌",
            responses = {
                    @ApiResponse(responseCode = "200", description = "令牌获取成功"),
                    @ApiResponse(responseCode = "400", description = "无效的授权码")
            }
    )
    @GetMapping("/token")
    @Parameter(description = "授权码", required = true, in = ParameterIn.QUERY)
    public R<IamTokenInfo> getToken(@RequestParam String code) {
        IamTokenInfo tokenInfo = remoteInfoService.getTokenInfo(code);
        if (ObjUtil.isNull(tokenInfo)) {
            return R.failed("获取token失败");
        }
        IamUserInfo userInfo = remoteInfoService.getUserInfo(tokenInfo.getAccess_token());
        remoteInfoService.getPermissionInfo(tokenInfo.getAccess_token(), userInfo.getUid());
        return R.ok(tokenInfo);
    }

    /**
     * 获取用户信息
     *
     * @return {@link R }<{@link IamUserInfo }>
     */
    @Operation(
            summary = "获取用户信息",
            description = "获取当前认证用户的详细信息",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "用户信息获取成功")
    @GetMapping("/userinfo")
    public R<SecurityContext> getUserInfo() {
        return R.ok(SecurityContextHolder.getContext());
    }

    /**
     * 通过token从 TAM 远程注销
     *
     * @param authorization 授权
     * @return {@link R }<{@link Boolean }>
     */
    @Operation(
            summary = "注销登录",
            description = "注销当前登录状态及上下文权限，使当前访问令牌失效",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "注销成功")
    @Parameter(description = "访问令牌", required = true, in = ParameterIn.HEADER, example = "Bearer access_token")
    @GetMapping("/logout")
    public R<Boolean> logout(@RequestHeader String authorization) {
        return R.ok(remoteInfoService.remoteLogout(authorization));
    }

    /**
     * 刷新 SecurityContextHolder及缓存中的token信息
     * <p>
     * 注意：这里只做清除，清除后下一次请求接口时会走过滤器重新缓存token和加载上下文权限信息
     *
     * @param authorization 授权
     * @return {@link R }<{@link Boolean }>
     * @see com.trinasolar.common.security.interceptor.SecurityInterceptor
     */
    @Operation(
            summary = "刷新权限上下文信息",
            description = "重新加载权限数据",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "刷新成功")
    @Parameter(description = "访问令牌", required = true, in = ParameterIn.HEADER, example = "Bearer access_token")
    @GetMapping("/refresh")
    public R<Boolean> refresh(@RequestHeader String authorization) {
        String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
        return R.ok(remoteInfoService.clearCache(token));
    }

    @Operation(
            summary = "获取用户应用菜单信息",
            description = "获取当前认证用户的已有应用菜单树信息",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "用户应用菜单信息获取成功")
    @GetMapping("/menu")
    public R<com.alibaba.fastjson2.JSONArray> getMenu(@RequestHeader String authorization) {
        return R.ok(remoteInfoService.remoteMenu(authorization));
    }

    @Operation(
            summary = "获取用户应用按钮权限信息",
            description = "获取当前认证用户的已有应用按钮信息",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "用户应用按钮权限信息获取成功")
    @GetMapping("/auth")
    public R<com.alibaba.fastjson2.JSONObject> getBtnAuth(@RequestHeader String authorization) {
        return R.ok(remoteInfoService.remoteBtnAuth(authorization));
    }
}
