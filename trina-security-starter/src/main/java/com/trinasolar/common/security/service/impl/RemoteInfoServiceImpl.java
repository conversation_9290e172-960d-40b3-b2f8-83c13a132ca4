package com.trinasolar.common.security.service.impl;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.common.core.constant.CacheConstants;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.domain.IamTokenInfo;
import com.trinasolar.common.security.domain.IamUserInfo;
import com.trinasolar.common.security.domain.TaspPermissionInfo;
import com.trinasolar.common.security.domain.TaspRoleInfo;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.common.security.service.RemoteInfoService;
import com.trinasolar.common.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Collections;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @since 2025-07-30 19:53
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class RemoteInfoServiceImpl implements RemoteInfoService {

    private final SecurityProperties securityProperties;
    private final RestTemplate restTemplate;
    private final Cache<String, Object> iamCache;

    @Override
    public IamTokenInfo getTokenInfo(String code) {
        try {
            String url = securityProperties.getEnvUrl() + SecurityConstant.TOKEN_PATH;

            MultiValueMap<String, Object> formBody = new LinkedMultiValueMap<>();
            formBody.add("grant_type", "authorization_code");
            formBody.add("client_id", securityProperties.getClientId());
            formBody.add("client_secret", securityProperties.getClientSecret());
            formBody.add("redirect_uri", securityProperties.getRedirectUrl());
            formBody.add("code", code);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(formBody, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
            String body = response.getBody();
            if (response.getStatusCode().is2xxSuccessful() && body != null) {
                IamTokenInfo tokenInfo = JSON.parseObject(body, IamTokenInfo.class);
                log.debug("remote get tokenInfo success: {}", tokenInfo);
                iamCache.put(CacheConstants.buildTokenInfoKey(SecurityUtils.buildToken(tokenInfo.getAccess_token())), tokenInfo.getAccess_token());
                return tokenInfo;
            } else {
                com.alibaba.fastjson2.JSONObject parseObj = JSON.parseObject(body);
                if (ObjUtil.isNotNull(body)) {
                    log.error("remote get tokenInfo error [{}],error_description[{}]", parseObj.getString("error"), parseObj.getString("error_description"));
                }
                log.error("remote get tokenInfo error with status [{}], body [{}]", response.getStatusCode(), response.getBody());
                return null;
            }
        } catch (Exception e) {
            log.error("remote get tokenInfo exception", e);
            return null;
        }
    }

    @Override
    public IamUserInfo getUserInfo(String token) {
        try {
            Object ifPresent = iamCache.getIfPresent(CacheConstants.buildUserInfoKey(SecurityUtils.buildToken(token)));
            if (ifPresent != null) {
                log.debug("Get userInfo from cache: {}", ifPresent);
                return (IamUserInfo) ifPresent;
            }

            String url = securityProperties.getEnvUrl() + SecurityConstant.USERINFO_PATH;

            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.set(HttpHeaders.AUTHORIZATION, SecurityUtils.buildAuthorization(token));

            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.GET, requestEntity, String.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                String body = response.getBody();
                IamUserInfo userInfo = JSON.parseObject(body, IamUserInfo.class);
                iamCache.put(CacheConstants.buildUserInfoKey(SecurityUtils.buildToken(token)), userInfo);
                log.debug("Remote get userInfo: {}", userInfo);
                return userInfo;
            }

            log.error("Remote userinfo request failed with status: {}", response.getStatusCode());
            return null;

        } catch (Exception e) {
            log.error("Failed to get user info from remote", e);
            return null;
        }
    }

    @Override
    public TaspPermissionInfo getPermissionInfo(String token, String userId) {
        try {
            log.debug("Loading user permissions from remote for user: {}", userId);

            Object ifPresent = iamCache.getIfPresent(CacheConstants.buildAuthInfoKey(SecurityUtils.buildToken(token)));
            if (ifPresent != null) {
                log.debug("Get permissionInfo from cache: {}", ifPresent);
                return (TaspPermissionInfo) ifPresent;
            }
            String url = UriComponentsBuilder.fromUriString(securityProperties.getTaspUrl() + SecurityConstant.TASP_AUTHORITY_PATH)
                    .queryParam("appId", securityProperties.getAppId())
                    .queryParam("username", userId)
                    .build()
                    .toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.set(HttpHeaders.AUTHORIZATION, SecurityUtils.buildAuthorization(token));

            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    requestEntity,
                    String.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                String body = response.getBody();
                log.debug("Successfully loaded permissions for user: {}", userId);
                com.alibaba.fastjson2.JSONObject parseObj = JSON.parseObject(body);
                Integer code = parseObj.getInteger("code");
                String message = parseObj.getString("message");
                Object data = parseObj.get("data");
                if (0 == code && data != null) {
                    com.alibaba.fastjson2.JSONObject dataObj = com.alibaba.fastjson2.JSONObject.from(data);
                    List<String> permissions = dataObj.getList("permissions", String.class);
                    List<TaspRoleInfo> roles = dataObj.getList("roles", TaspRoleInfo.class);

                    TaspPermissionInfo permissionInfo = new TaspPermissionInfo();
                    permissionInfo.setPermissions(permissions);
                    if (CollectionUtils.isEmpty(roles)) {
                        permissionInfo.setRoleCodes(List.of());
                        permissionInfo.setRoles(List.of());
                    } else {
                        permissionInfo.setRoleCodes(roles.stream().map(TaspRoleInfo::getCode).toList());
                        permissionInfo.setRoles(roles);
                    }
                    iamCache.put(CacheConstants.buildAuthInfoKey(SecurityUtils.buildToken(token)), permissionInfo);
                    return permissionInfo;
                } else {
                    log.warn("Failed to load permissions for user: {}, message: {}", userId, message);
                }
            } else {
                log.warn("Failed to load permissions for user: {}, status: {}", userId, response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Error loading user permissions from remote for user: {}", userId, e);
        }
        return null;
    }

    @Override
    public boolean remoteLogout(String token) {
        try {
            String url = securityProperties.getEnvUrl() + SecurityConstant.LOGOUT_PATH + securityProperties.getClientId() + SecurityConstant.REDIRECT_URI + securityProperties.getRedirectUrl();

            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.set(HttpHeaders.AUTHORIZATION, SecurityUtils.buildAuthorization(token));

            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.GET, requestEntity, String.class
            );
            if (response.getStatusCode().is2xxSuccessful()) {
                log.debug("remote logout success");
                clearCache(token);
                return true;
            } else {
                log.error("remote get logout error with status [{}], body [{}]", response.getStatusCode(), response.getBody());
                return false;
            }
        } catch (Exception e) {
            log.error("remote get logout exception", e);
            return false;
        }
    }

    @Override
    public com.alibaba.fastjson2.JSONArray remoteMenu(String token) {
        try {
            Object ifPresent = iamCache.getIfPresent(CacheConstants.buildMenuInfoKey(SecurityUtils.buildToken(token)));
            if (ifPresent != null) {
                log.debug("remote get menus from cache");
                return (com.alibaba.fastjson2.JSONArray) ifPresent;
            }

            String url = securityProperties.getTaspUrl() + SecurityConstant.TASP_MENU_PATH + StrPool.SLASH + securityProperties.getAppId();

            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.set(HttpHeaders.AUTHORIZATION, SecurityUtils.buildAuthorization(token));

            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.GET, requestEntity, String.class
            );
            if (response.getStatusCode().is2xxSuccessful()) {
                log.debug("remote get menus success");
                String body = response.getBody();
                com.alibaba.fastjson2.JSONObject bodyObj = JSON.parseObject(body);
                com.alibaba.fastjson2.JSONArray data = bodyObj.getJSONArray("data");
                iamCache.put(CacheConstants.buildMenuInfoKey(SecurityUtils.buildToken(token)), data);
                return data;
            } else {
                log.error("remote get menus error with status [{}], body [{}]", response.getStatusCode(), response.getBody());
                return null;
            }
        } catch (Exception e) {
            log.error("remote get menus exception", e);
            return null;
        }
    }

    @Override
    public com.alibaba.fastjson2.JSONObject remoteBtnAuth(String token) {
        try {
            Object ifPresent = iamCache.getIfPresent(CacheConstants.buildBtnInfoKey(SecurityUtils.buildToken(token)));
            if (ifPresent != null) {
                log.debug("remote get btn auth from cache");
                return (com.alibaba.fastjson2.JSONObject) ifPresent;
            }
            String url = securityProperties.getTaspUrl() + SecurityConstant.TASP_AUTH_PATH + StrPool.SLASH + securityProperties.getAppId();

            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.set(HttpHeaders.AUTHORIZATION, SecurityUtils.buildAuthorization(token));

            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.GET, requestEntity, String.class
            );
            if (response.getStatusCode().is2xxSuccessful()) {
                log.debug("remote get btn auth success");
                String body = response.getBody();
                com.alibaba.fastjson2.JSONObject bodyObj = JSON.parseObject(body);
                com.alibaba.fastjson2.JSONObject data = bodyObj.getJSONObject("data");
                iamCache.put(CacheConstants.buildBtnInfoKey(SecurityUtils.buildToken(token)), data);
                return data;
            } else {
                log.error("remote get btn auth error with status [{}], body [{}]", response.getStatusCode(), response.getBody());
                return null;
            }
        } catch (Exception e) {
            log.error("remote get btn auth exception", e);
            return null;
        }
    }

    @Override
    public boolean clearCache(String token) {
        SecurityContextHolder.clearContext();
        iamCache.asMap().remove(CacheConstants.buildUserInfoKey(SecurityUtils.buildToken(token)));
        iamCache.asMap().remove(CacheConstants.buildMenuInfoKey(SecurityUtils.buildToken(token)));
        iamCache.asMap().remove(CacheConstants.buildBtnInfoKey(SecurityUtils.buildToken(token)));
        iamCache.asMap().remove(CacheConstants.buildAuthInfoKey(SecurityUtils.buildToken(token)));
        return true;
    }
}
