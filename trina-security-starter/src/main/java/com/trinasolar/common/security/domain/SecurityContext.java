package com.trinasolar.common.security.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 安全上下文模型
 * <p>
 * 按业内规范优化，包含完整的用户认证和授权信息
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
public class SecurityContext implements Serializable {

    @Serial
    private static final long serialVersionUID = -6635618517728581803L;

    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String accessToken;

    @Schema(description = "认证时间", example = "2025-06-26T09:30:00")
    private LocalDateTime authenticatedAt;

    @Schema(description = "认证状态", example = "true")
    private boolean authenticated;

    @Schema(description = "用户唯一标识", example = "USER_001")
    private String userId;

    @Schema(description = "用户名", example = "张三")
    private String username;

    @Schema(description = "显示名称", example = "张三")
    private String displayName;

    @Schema(description = "电子邮箱", example = "<EMAIL>")
    private String email;

    private String deptId;
    private String employeeNumber;
    private String postType;
    private String location;
    private String filled;

    @Schema(description = "用户角色列表", example = "[\"ADMIN\", \"USER\"]")
    private List<String> roleCodes;

    @Schema(description = "权限列表", example = "[\"user:read\", \"user:write\"]")
    private List<String> permissions;

    @Schema(description = "数据权限范围")
    private List<TaspRoleInfo> roles;

    /**
     * 检查用户是否拥有指定角色
     */
    public boolean hasRole(String role) {
        return getRoleCodes() != null && getRoleCodes().contains(role);
    }

    /**
     * 检查用户是否拥有任意一个角色
     */
    public boolean hasAnyRole(String... roles) {
        if (getRoleCodes() == null) {
            return false;
        }
        for (String role : roles) {
            if (getRoleCodes().contains(role)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 检查用户是否拥有所有角色
     */
    public boolean hasAllRoles(String... roles) {
        if (getRoleCodes() == null) {
            return false;
        }
        for (String role : roles) {
            if (!getRoleCodes().contains(role)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查用户是否拥有指定权限
     */
    public boolean hasPermission(String permission) {
        return getPermissions() != null && getPermissions().contains(permission);
    }

    /**
     * 检查用户是否拥有任意一个权限
     */
    public boolean hasAnyPermission(String... permissions) {
        if (getPermissions() == null) {
            return false;
        }
        for (String permission : permissions) {
            if (getPermissions().contains(permission)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查用户是否拥有所有权限
     */
    public boolean hasAllPermissions(String... permissions) {
        if (getPermissions() == null) {
            return false;
        }
        for (String permission : permissions) {
            if (!getPermissions().contains(permission)) {
                return false;
            }
        }
        return true;
    }
}
