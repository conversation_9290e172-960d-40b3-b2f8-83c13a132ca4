package com.trinasolar.common.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.common.security.aspect.ColumnPermissionAspect;
import com.trinasolar.common.security.aspect.PermissionAspect;
import com.trinasolar.common.security.aspect.RolePermissionAspect;
import com.trinasolar.common.security.cache.IamCaffeineCacheConfiguration;
import com.trinasolar.common.security.endpoint.AuthEndpoint;
import com.trinasolar.common.security.interceptor.SecurityInterceptor;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.common.security.service.RemoteInfoService;
import com.trinasolar.common.security.service.impl.RemoteInfoServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.client.RestTemplate;

/**
 * 自动安全配置
 * <p>
 * 启用AOP支持，配置RestTemplate和权限服务
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@AutoConfigureAfter(IamCaffeineCacheConfiguration.class)
@RequiredArgsConstructor
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@EnableConfigurationProperties(SecurityProperties.class)
public class AutoSecurityConfiguration {

    private final SecurityProperties securityProperties;

    @Bean
    @ConditionalOnMissingBean
    public AuthEndpoint authEndpoint(RemoteInfoService remoteInfoService) {
        return new AuthEndpoint(remoteInfoService);
    }

    @Bean
    public FilterRegistrationBean<SecurityInterceptor> registerMyFilter(Cache<String, Object> iamCache, RemoteInfoService remoteInfoService, ObjectMapper objectMapper) {
        FilterRegistrationBean<SecurityInterceptor> registration = new FilterRegistrationBean<>();
        registration.setFilter(new SecurityInterceptor(securityProperties, iamCache, remoteInfoService, objectMapper));
        registration.addUrlPatterns("/*");
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE + 3);
        return registration;
    }

    @Bean
    @ConditionalOnMissingBean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    @Bean
    @ConditionalOnMissingBean
    public RemoteInfoService remoteInfoService(RestTemplate restTemplate, Cache<String, Object> iamCache) {
        return new RemoteInfoServiceImpl(securityProperties, restTemplate, iamCache);
    }

    @Bean
    @Order(100)
    public RolePermissionAspect rolePermissionAspect() {
        return new RolePermissionAspect();
    }

    @Bean
    @Order(200)
    public PermissionAspect permissionAspect() {
        return new PermissionAspect();
    }

    @Bean
    @Order(300)
    public ColumnPermissionAspect columnPermissionAspect(ObjectMapper objectMapper) {
        return new ColumnPermissionAspect(objectMapper);
    }
}
