package com.trinasolar.common.security.service;


import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.trinasolar.common.security.domain.IamTokenInfo;
import com.trinasolar.common.security.domain.IamUserInfo;
import com.trinasolar.common.security.domain.SecurityContext;
import com.trinasolar.common.security.domain.TaspPermissionInfo;

/**
 * 远程服务
 *
 * <AUTHOR>
 * @date 2025-07-30 19:53
 */
public interface RemoteInfoService {

    /**
     * 远程获取token信息
     *
     * @param code 授权码
     * @return {@link IamTokenInfo }
     */
    IamTokenInfo getTokenInfo(String code);

    /**
     * 远程获取用户信息
     *
     * @param token token
     * @return {@link IamUserInfo }
     */
    IamUserInfo getUserInfo(String token);

    /**
     * 获取权限信息
     *
     * @param token token
     * @param userId 用户id
     * @return {@link SecurityContext }
     */
    TaspPermissionInfo getPermissionInfo(String token, String userId);

    /**
     * 远程注销
     *
     * @param token token
     * @return {@link Boolean }
     */
    boolean remoteLogout(String token);

    /**
     * 远程获取菜单
     *
     * @param token token
     * @return {@link JSONArray }
     */
    com.alibaba.fastjson2.JSONArray remoteMenu(String token);

    /**
     * 远程获取按钮权限信息
     *
     * @param token token
     * @return {@link JSONObject }
     */
    com.alibaba.fastjson2.JSONObject remoteBtnAuth(String token);

    /**
     * 清除缓存
     *
     * @param token token
     * @return boolean
     */
    boolean clearCache(String token);
}
