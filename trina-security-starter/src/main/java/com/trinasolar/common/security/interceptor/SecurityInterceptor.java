package com.trinasolar.common.security.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.common.core.constant.CacheConstants;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.domain.IamUserInfo;
import com.trinasolar.common.security.domain.TaspPermissionInfo;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.common.security.service.RemoteInfoService;
import com.trinasolar.common.security.util.SecurityUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;

/**
 * 安全拦截器
 * <p>
 * iamCache只缓存token验证结果，设置适当过期时间
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SecurityInterceptor extends OncePerRequestFilter {

    private final SecurityProperties securityProperties;
    private final Cache<String, Object> iamCache;
    private final RemoteInfoService remoteInfoService;
    private final ObjectMapper objectMapper;

    @Override
    @SuppressWarnings("all")
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String path = request.getRequestURI();
        String contextPath = request.getContextPath();
        String authorization = request.getHeader(HttpHeaders.AUTHORIZATION);
        try {
            log.debug("Security interceptor processing request: {} with authorization: {}", path, authorization != null ? "Bearer ***" : "null");

            // 1. 默认白名单处理
            if (SecurityUtils.matchWhiteUrl(SecurityUtils.DEFAULT_WHITE_URLS, path, contextPath)) {
                filterChain.doFilter(request, response);
                return;
            }

            // 2. uri 白名单处理
            List<String> whiteUrls = securityProperties.getWhiteUris();
            if (SecurityUtils.matchWhiteUrl(whiteUrls, path, contextPath)) {
                filterChain.doFilter(request, response);
                return;
            }

            // 3. 验证token是否有效

            if (StringUtils.hasText(authorization) && authorization.startsWith(SecurityConstant.TOKEN_PREFIX)) {
                if (!verifyToken(authorization)) {
                    log.error("Token verification failed for request: {}", path);
                    writeErrorMessage(response, "token 无效，验证失败");
                    return;
                }
            } else {
                writeErrorMessage(response, "token 为空");
                return;
            }
            filterChain.doFilter(request, response);
        } catch (Exception e) {
            log.error("Token verification exception for request: {}", path, e);
            writeErrorMessage(response, "token 验证异常");
        }
    }

    /**
     * 验证token
     * <p>
     * 优化缓存策略：缓存用户信息，避免重复远程调用
     * 每次请求都需要设置SecurityContext到ThreadLocal
     */
    private boolean verifyToken(String authorization) {
        try {
            String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
            String tokenCacheKey = CacheConstants.buildTokenInfoKey(token);

            // 先检查缓存
            Object ifPresent = iamCache.getIfPresent(tokenCacheKey);
            log.debug("Token cache lookup - key: {}, found: {}, cache size: {}", tokenCacheKey, ifPresent != null, iamCache.estimatedSize());
            IamUserInfo userInfo = remoteInfoService.getUserInfo(token);
            if (!ObjectUtils.isEmpty(userInfo)) {
                TaspPermissionInfo permissionInfo = remoteInfoService.getPermissionInfo(token, userInfo.getUid());
                if (!ObjectUtils.isEmpty(permissionInfo)) {
                    // 构建安全上下文
                    SecurityUtils.setSecurityContext(token, userInfo, permissionInfo);
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("Error verifying token and setting context", e);
            return false;
        }
    }

    /**
     * 写入错误消息
     *
     * @param response 响应
     * @param message  消息
     * @throws IOException IOException
     */
    private void writeErrorMessage(HttpServletResponse response, String message) throws IOException {
        String authorizeUrl = securityProperties.getEnvUrl() + SecurityConstant.AUTHORIZE_PATH + securityProperties.getClientId() + SecurityConstant.REDIRECT_URI + securityProperties.getRedirectUrl();
        HashMap<String, Object> result = new HashMap<>();
        result.put("code", 1);
        result.put("msg", message);
        result.put("data", authorizeUrl);

        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(objectMapper.writeValueAsString(result));
        response.getWriter().flush();
    }
}
