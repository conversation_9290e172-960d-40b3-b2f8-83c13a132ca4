package com.trinasolar.data.datascope;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.trinasolar.common.security.domain.TaspRoleInfo;
import com.trinasolar.common.security.util.SecurityUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025-07-15 10:30
 **/
public class DefaultDataScopeHandle implements DataScopeHandle {

    @Override
    public Boolean calcScope(DataScope dataScope) {
        // 业务代码里的规则，覆盖计算规则
        if (StrUtil.isNotBlank(dataScope.getUsername()) || CollUtil.isNotEmpty(dataScope.getOrgIdList())) {
            return false;
        }

        // 获取用户角色ID列表
        List<TaspRoleInfo> roleList = SecurityUtils.getCurrentUserRoles();
        if (CollUtil.isEmpty(roleList)) {
            return false;
        }

        // 处理数据权限
        return processDataScope(dataScope, roleList);
    }


    /**
     * 处理数据权限
     */
    private boolean processDataScope(DataScope dataScope, List<TaspRoleInfo> roleList) {
        List<String> orgList = dataScope.getOrgIdList();

        for (TaspRoleInfo role : roleList) {
            Integer dsType = role.getDsType();

            // 处理不同数据权限类型
            switch (Objects.requireNonNull(DataScopeTypeEnum.getByType(dsType))) {
                case ALL:
                    return true;
                case CUSTOM:
                    handleCustomScope(role, orgList);
                    break;
                case OWN_CHILD_LEVEL:
                    handleOwnChildLevelScope(role, orgList);
                    break;
                case OWN_LEVEL:
                    handleOwnLevelScope(role, orgList);
                    break;
                case SELF_LEVEL:
                    handleSelfLevelScope(dataScope);
                    break;
                default:
                    break;
            }
        }

        return false;
    }

    /**
     * 处理自定义数据权限
     */
    private void handleCustomScope(TaspRoleInfo role, List<String> orgList) {
        if (CollUtil.isNotEmpty(role.getDsScope())) {
            orgList.addAll(role.getDsScope());
        }
    }

    /**
     * 处理本级及下级数据权限
     */
    private void handleOwnChildLevelScope(TaspRoleInfo role, List<String> orgList) {
        if (CollUtil.isNotEmpty(role.getDsScope())) {
            orgList.addAll(role.getDsScope());
        }
    }

    /**
     * 处理本级数据权限
     */
    private void handleOwnLevelScope(TaspRoleInfo role, List<String> orgList) {
        if (CollUtil.isNotEmpty(role.getDsScope())) {
            orgList.addAll(role.getDsScope());
        }
    }

    /**
     * 处理个人数据权限
     */
    private void handleSelfLevelScope(DataScope dataScope) {
        dataScope.setUsername(SecurityUtils.getCurrentUsername());
    }
}
