package com.trinasolar.gateway.security.endpoint;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.trinasolar.common.core.base.R;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.domain.IamTokenInfo;
import com.trinasolar.common.security.domain.IamUserInfo;
import com.trinasolar.common.security.domain.SecurityContext;
import com.trinasolar.common.security.service.RemoteInfoService;
import com.trinasolar.gateway.security.config.ApiPathInit;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * 网关 auth 端点
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/scf")
@Tag(name = "认证管理", description = "IAM认证相关接口")
public class GatewayAuthEndpoint {

    private final RemoteInfoService remoteInfoService;

    /**
     * 通过授权码从 TAM 远程获取令牌
     *
     * @param code 授权码
     * @return {@link R }<{@link IamTokenInfo }>
     */
    @Operation(
            summary = "获取访问令牌",
            description = "通过授权码模式获取IAM访问令牌",
            responses = {
                    @ApiResponse(responseCode = "200", description = "令牌获取成功"),
                    @ApiResponse(responseCode = "400", description = "无效的授权码")
            }
    )
    @GetMapping("/token")
    @Parameter(description = "授权码", required = true, in = ParameterIn.QUERY)
    public Mono<R<IamTokenInfo>> getToken(@RequestParam String code) {
        IamTokenInfo tokenInfo = remoteInfoService.getTokenInfo(code);
        if (ObjUtil.isNull(tokenInfo)) {
            return Mono.just(R.failed("获取token失败"));
        }
        IamUserInfo userInfo = remoteInfoService.getUserInfo(tokenInfo.getAccess_token());
        //remoteInfoService.getPermissionInfo(tokenInfo.getAccess_token(), userInfo.getUid());
        return Mono.fromCallable(() -> R.ok(tokenInfo));
    }

    /**
     * 获取用户信息
     *
     * @return {@link R }<{@link IamUserInfo }>
     */
    @Operation(
            summary = "获取用户信息",
            description = "获取当前认证用户的详细信息",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "用户信息获取成功")
    @GetMapping("/userinfo")
    public Mono<R<SecurityContext>> getUserInfo() {
        return Mono.fromCallable(() -> R.ok(SecurityContextHolder.getContext()));
    }

    /**
     * 通过token从 TAM 远程注销
     *
     * @param authorization 授权
     * @return {@link R }<{@link Boolean }>
     */
    @Operation(
            summary = "注销登录",
            description = "注销当前登录状态及上下文权限，使当前访问令牌失效",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "注销成功")
    @Parameter(description = "访问令牌", required = true, in = ParameterIn.HEADER, example = "Bearer access_token")
    @GetMapping("/logout")
    public Mono<R<Boolean>> logout(@RequestHeader String authorization) {
        return Mono.fromCallable(() -> R.ok(remoteInfoService.remoteLogout(authorization)));
    }

    /**
     * 刷新 SecurityContextHolder及缓存中的token信息
     * <p>
     * 注意：这里只做清除，清除后下一次请求接口时会走过滤器重新缓存token和加载上下文权限信息
     *
     * @param authorization 授权
     * @return {@link R }<{@link Boolean }>
     * @see com.trinasolar.gateway.security.filter.GatewaySecurityFilter
     */
    @Operation(
            summary = "刷新权限上下文信息",
            description = "重新加载权限数据",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "刷新成功")
    @Parameter(description = "访问令牌", required = true, in = ParameterIn.HEADER, example = "Bearer access_token")
    @GetMapping("/refresh")
    public Mono<R<Boolean>> refresh(@RequestHeader String authorization) {
        // 初始化API路径（如果需要）
        try {
            ApiPathInit apiPathInit = SpringUtil.getBean(ApiPathInit.class);
            apiPathInit.init();
        } catch (Exception e) {
            log.debug("ApiPathInit not available or failed to initialize", e);
        }
        return Mono.fromCallable(() -> R.ok(remoteInfoService.clearCache(authorization)));
    }

    @Operation(
            summary = "获取用户应用菜单信息",
            description = "获取当前认证用户的已有应用菜单树信息",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "用户应用菜单信息获取成功")
    @GetMapping("/menu")
    public Mono<R<com.alibaba.fastjson2.JSONArray>> getMenu(@RequestHeader String authorization) {
        return Mono.fromCallable(() -> R.ok(remoteInfoService.remoteMenu(authorization)));
    }

    @Operation(
            summary = "获取用户应用按钮权限信息",
            description = "获取当前认证用户的已有应用按钮信息",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "用户应用按钮权限信息获取成功")
    @GetMapping("/auth")
    public Mono<R<com.alibaba.fastjson2.JSONObject>> getBtnAuth(@RequestHeader String authorization) {
        return Mono.fromCallable(() -> R.ok(remoteInfoService.remoteBtnAuth(authorization)));
    }
}
