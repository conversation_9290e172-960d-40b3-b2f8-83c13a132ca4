package com.trinasolar.gateway.security.util;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.common.core.constant.CacheConstants;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.domain.IamUserInfo;
import com.trinasolar.common.security.domain.TaspPermissionInfo;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.common.security.service.RemoteInfoService;
import com.trinasolar.common.security.util.SecurityUtils;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.util.ObjectUtils;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2025-07-14 10:58
 **/
@Slf4j
@UtilityClass
public class WebFluxUtils {

    // 新增token验证方法
    public Mono<Boolean> verifyTokenAndSetContext(String authorization, Cache<String, Object> iamCache, RemoteInfoService remoteInfoService) {
        try {
            String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
            String tokenCacheKey = CacheConstants.buildTokenInfoKey(token);

            // 先检查缓存
            Object ifPresent = iamCache.getIfPresent(tokenCacheKey);
            log.debug("Token cache lookup - key: {}, found: {}, cache size: {}", tokenCacheKey, ifPresent != null, iamCache.estimatedSize());
            IamUserInfo userInfo = remoteInfoService.getUserInfo(token);
            if (!ObjectUtils.isEmpty(userInfo)) {
                //TaspPermissionInfo permissionInfo = remoteInfoService.getPermissionInfo(token, userInfo.getUid());
                //if (!ObjectUtils.isEmpty(permissionInfo)) {
                //    // 构建安全上下文
                //    SecurityUtils.setSecurityContext(token, userInfo, permissionInfo);
                //    return Mono.just(true);
                //}
                SecurityUtils.setSecurityContext(token, userInfo, new TaspPermissionInfo());
                    return Mono.just(true);
            }
            return Mono.just(false);
        } catch (Exception e) {
            log.error("Error verifying token and setting context", e);
            return Mono.just(false);
        }
    }

    /**
     * 处理未授权错误
     *
     * @param securityProperties 安全属性
     * @param objectMapper       对象映射器
     * @param response           响应
     * @param message            消息
     * @return {@link Mono }<{@link Void }>
     */
    public Mono<Void> handleUnauthorized(SecurityProperties securityProperties, ObjectMapper objectMapper, ServerHttpResponse response, String message) {
        String authorizeUrl = securityProperties.getEnvUrl() + SecurityConstant.AUTHORIZE_PATH +
                securityProperties.getClientId() + SecurityConstant.REDIRECT_URI +
                securityProperties.getRedirectUrl();
        return ErrorResponseUtils.writeUnauthorizedResponse(response, objectMapper, message, authorizeUrl);
    }

    /**
     * 处理权限不足错误
     *
     * @param objectMapper 对象映射器
     * @param response     响应
     * @param message      消息
     * @return {@link Mono }<{@link Void }>
     */
    public Mono<Void> handleForbidden(ObjectMapper objectMapper, ServerHttpResponse response, String message) {
        return ErrorResponseUtils.writeForbiddenResponse(response, objectMapper, message);
    }

    // 添加新方法：构建下游请求头
    public ServerHttpRequest addDownstreamHeaders(ServerHttpRequest request) {
        return request.mutate()
                .header(SecurityUtils.HEADER_USER_ID, SecurityContextHolder.getCurrentUserId())
                .header(HttpHeaders.AUTHORIZATION, request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION))
                .build();
    }
}
