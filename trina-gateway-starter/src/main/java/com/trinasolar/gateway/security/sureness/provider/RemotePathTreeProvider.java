package com.trinasolar.gateway.security.sureness.provider;


import cn.hutool.core.text.StrPool;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.common.core.constant.CacheConstants;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.properties.SecurityProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.Set;

/**
 *  权限服务端提供权限数据
 *
 * <AUTHOR>
 * @since 2025-07-28 17:58
 **/
@Slf4j
@RequiredArgsConstructor
public class RemotePathTreeProvider implements PathTreeProvider {

    private final SecurityProperties securityProperties;
    private final RestTemplate restTemplate;
    private final Cache<String, Object> iamCache;

    @Override
    public Set<String> providePathData() {
        //调用远程认证服务并同步等待结果（根据响应式框架特性可能需要调整）
        return remoteApiPathAuth();
        //return Set.of("/kepler/upms/u/users/**===put===[2asda,admin,app_admin,app_dvlpr,bbyqgly,cehsi,ceshipath,child1,cjadmin,cjgkyzjs,cjsczhzxddry,clglzxfjgly,cloudaccountadmin,cmpt_admin,cmpt_dvlpr,cpcsadmin,cpcsuser1,cqecfjgly,cqscfjgly,cqshcfjgly,cqycfjgly,cyjdfjgly,dynamic-home-admin,ewewreer,fgsgly,gcjsyjyfjgly,glqcxsjsh,glqsczhzxddry,gxjdfjgly,gyjhfjgly,gyszxfjgly,gzjdfjgly,hnjdfjgly,hxgsfjgly,jcztgly,jdzxfjgly,jhadmin,jyglzxfjgly,ktfgsfjgly,ktkfyjyfjgly,lxgsfjgly,pcs-fjs-test,pcs-zjs,pcsadmin,pxzxfjgly,scsygsfjgly,testcpp,threemenutest,tset,usermanage,wordOrderInspection,wqwqwq,wzgyzxfjgly,xclyfjgly,xmglbfjgly,xngcgsfjgly,YJZHCSRY,ynjdfjgly,ynrqgsfjgly,yqxsfjgly,yw605fjgly,yxdtclyxshy,yxdtxlyxshy,zijuesesasd,zjrqgsfjgly,zsxnhwtest]",
        //        "/kepler/upms/u/orgs/**===get===[2asda,admin,app_admin,app_dvlpr,bbyqgly,cehsi,ceshipath,child1,cjadmin,cjgkyzjs,cjsczhzxddry,clglzxfjgly,cloudaccountadmin,cmpt_admin,cmpt_dvlpr,cpcsadmin,cpcsuser1,cqecfjgly,cqscfjgly,cqshcfjgly,cqycfjgly,cyjdfjgly,dynamic-home-admin,ewewreer,fgsgly,gcjsyjyfjgly,glqcxsjsh,glqsczhzxddry,gxjdfjgly,gyjhfjgly,gyszxfjgly,gzjdfjgly,hnjdfjgly,hxgsfjgly,jcztgly,jdzxfjgly,jhadmin,jyglzxfjgly,ktfgsfjgly,ktkfyjyfjgly,lxgsfjgly,pcs-fjs-test,pcs-zjs,pcsadmin,pxzxfjgly,qxfu-mmm,scsygsfjgly,testcpp,threemenutest,tset,usermanage,wordOrderInspection,wqwqwq,wzgyzxfjgly,xclyfjgly,xmglbfjgly,xngcgsfjgly,YJZHCSRY,ynjdfjgly,ynrqgsfjgly,yqxsfjgly,yw605fjgly,yxdtclyxshy,yxdtxlyxshy,zijuesesasd,zjrqgsfjgly,zsxnhwtest]",
        //        "/api/bdu/**===get===[scftest]");
    }

    @Override
    public Set<String> provideExcludedResource() {
        return Set.of();
    }


    /**
     * 远程获取API PATH权限资源
     *
     * @return {@link JSONArray }
     */
    private Set<String> remoteApiPathAuth() {
        String url = securityProperties.getTaspUrl() + SecurityConstant.TASP_API_AUTH_PATH + StrPool.SLASH + securityProperties.getAppId();
        Object cacheMenu = iamCache.getIfPresent(CacheConstants.buildApiAuthInfoKey(securityProperties.getAppId()));
        if (!ObjectUtils.isEmpty(cacheMenu)) {
            @SuppressWarnings("unchecked")
            Set<String> cachedSet = (Set<String>) cacheMenu;
            return cachedSet;
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

        ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                requestEntity,
                String.class
        );

        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
            String body = response.getBody();
            JSONObject bodyObj = JSONObject.parseObject(body);
            // 使用TypeReference指定泛型类型
            Set<String> data = bodyObj.getObject("data", new TypeReference<Set<String>>() {
            });
            iamCache.put(CacheConstants.buildApiAuthInfoKey(securityProperties.getAppId()), data);
            return data;
        }
        return Set.of();
    }
}
