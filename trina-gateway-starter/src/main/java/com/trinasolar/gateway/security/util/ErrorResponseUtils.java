package com.trinasolar.gateway.security.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.common.core.base.R;
import com.trinasolar.common.core.constant.CommonConstants;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 错误响应工具类
 * <p>
 * 统一处理网关中的错误响应，确保所有错误都返回 R 结构（code、msg、data）
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@UtilityClass
public class ErrorResponseUtils {

    /**
     * 写入统一的错误响应
     *
     * @param response     HTTP响应
     * @param objectMapper JSON序列化器
     * @param httpStatus   HTTP状态码
     * @param message      错误消息
     * @param data         错误数据（可选）
     * @return Mono<Void>
     */
    public static Mono<Void> writeErrorResponse(ServerHttpResponse response, ObjectMapper objectMapper,
                                                HttpStatus httpStatus, String message, Object data) {
        try {
            // 设置响应状态码和内容类型
            response.setStatusCode(httpStatus);
            response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
            response.getHeaders().add("Cache-Control", "no-cache, no-store, must-revalidate");
            response.getHeaders().add("Pragma", "no-cache");
            response.getHeaders().add("Expires", "0");

            // 构建统一的 R 结构响应
            R<Object> errorResponse = R.restResult(data, CommonConstants.FAIL, message);
            
            // 序列化响应体
            String responseBody = objectMapper.writeValueAsString(errorResponse);
            DataBuffer buffer = response.bufferFactory().wrap(responseBody.getBytes(StandardCharsets.UTF_8));
            
            return response.writeWith(Mono.just(buffer));
        } catch (Exception e) {
            log.error("Error writing error response", e);
            return writeSimpleErrorResponse(response, httpStatus, message);
        }
    }

    /**
     * 写入简单的错误响应（当序列化失败时使用）
     */
    private static Mono<Void> writeSimpleErrorResponse(ServerHttpResponse response, HttpStatus httpStatus, String message) {
        try {
            response.setStatusCode(httpStatus);
            response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
            
            String simpleResponse = String.format(
                "{\"code\":%d,\"msg\":\"%s\",\"data\":null}", 
                CommonConstants.FAIL, 
                message.replace("\"", "\\\"")
            );
            
            DataBuffer buffer = response.bufferFactory().wrap(simpleResponse.getBytes(StandardCharsets.UTF_8));
            return response.writeWith(Mono.just(buffer));
        } catch (Exception e) {
            log.error("Error writing simple error response", e);
            return Mono.empty();
        }
    }

    /**
     * 写入认证失败响应
     *
     * @param response           HTTP响应
     * @param objectMapper       JSON序列化器
     * @param message            错误消息
     * @param authorizeUrl       授权URL（可选）
     * @return Mono<Void>
     */
    public static Mono<Void> writeUnauthorizedResponse(ServerHttpResponse response, ObjectMapper objectMapper,
                                                       String message, String authorizeUrl) {
        return writeErrorResponse(response, objectMapper, HttpStatus.UNAUTHORIZED, message, authorizeUrl);
    }

    /**
     * 写入权限不足响应
     *
     * @param response     HTTP响应
     * @param objectMapper JSON序列化器
     * @param message      错误消息
     * @return Mono<Void>
     */
    public static Mono<Void> writeForbiddenResponse(ServerHttpResponse response, ObjectMapper objectMapper, String message) {
        Map<String, Object> data = new HashMap<>();
        data.put("timestamp", LocalDateTime.now());
        
        return writeErrorResponse(response, objectMapper, HttpStatus.FORBIDDEN, message, data);
    }

    /**
     * 写入资源未找到响应
     *
     * @param response     HTTP响应
     * @param objectMapper JSON序列化器
     * @param path         请求路径
     * @return Mono<Void>
     */
    public static Mono<Void> writeNotFoundResponse(ServerHttpResponse response, ObjectMapper objectMapper, String path) {
        Map<String, Object> data = new HashMap<>();
        data.put("path", path);
        data.put("timestamp", LocalDateTime.now());
        
        return writeErrorResponse(response, objectMapper, HttpStatus.NOT_FOUND, "Not Found", data);
    }

    /**
     * 写入服务器内部错误响应
     *
     * @param response     HTTP响应
     * @param objectMapper JSON序列化器
     * @param message      错误消息
     * @param requestId    请求ID（可选）
     * @return Mono<Void>
     */
    public static Mono<Void> writeInternalServerErrorResponse(ServerHttpResponse response, ObjectMapper objectMapper,
                                                              String message, String requestId) {
        Map<String, Object> data = new HashMap<>();
        if (requestId != null) {
            data.put("requestId", requestId);
        }
        data.put("timestamp", LocalDateTime.now());
        
        return writeErrorResponse(response, objectMapper, HttpStatus.INTERNAL_SERVER_ERROR, message, data);
    }

    /**
     * 写入请求参数错误响应
     *
     * @param response     HTTP响应
     * @param objectMapper JSON序列化器
     * @param message      错误消息
     * @return Mono<Void>
     */
    public static Mono<Void> writeBadRequestResponse(ServerHttpResponse response, ObjectMapper objectMapper, String message) {
        Map<String, Object> data = new HashMap<>();
        data.put("timestamp", LocalDateTime.now());
        
        return writeErrorResponse(response, objectMapper, HttpStatus.BAD_REQUEST, message, data);
    }

    /**
     * 写入服务不可用响应
     *
     * @param response     HTTP响应
     * @param objectMapper JSON序列化器
     * @param message      错误消息
     * @return Mono<Void>
     */
    public static Mono<Void> writeServiceUnavailableResponse(ServerHttpResponse response, ObjectMapper objectMapper, String message) {
        Map<String, Object> data = new HashMap<>();
        data.put("timestamp", LocalDateTime.now());
        
        return writeErrorResponse(response, objectMapper, HttpStatus.SERVICE_UNAVAILABLE, message, data);
    }

    /**
     * 写入网关超时响应
     *
     * @param response     HTTP响应
     * @param objectMapper JSON序列化器
     * @param message      错误消息
     * @return Mono<Void>
     */
    public static Mono<Void> writeGatewayTimeoutResponse(ServerHttpResponse response, ObjectMapper objectMapper, String message) {
        Map<String, Object> data = new HashMap<>();
        data.put("timestamp", LocalDateTime.now());
        
        return writeErrorResponse(response, objectMapper, HttpStatus.GATEWAY_TIMEOUT, message, data);
    }

    /**
     * 根据异常类型写入相应的错误响应
     *
     * @param response     HTTP响应
     * @param objectMapper JSON序列化器
     * @param throwable    异常
     * @param requestPath  请求路径
     * @return Mono<Void>
     */
    public static Mono<Void> writeErrorResponseByException(ServerHttpResponse response, ObjectMapper objectMapper,
                                                           Throwable throwable, String requestPath) {
        String message = throwable.getMessage() != null ? throwable.getMessage() : throwable.getClass().getSimpleName();
        
        // 根据异常类型返回不同的HTTP状态码
        if (throwable instanceof IllegalArgumentException) {
            return writeBadRequestResponse(response, objectMapper, "请求参数错误: " + message);
        } else if (throwable instanceof SecurityException) {
            return writeForbiddenResponse(response, objectMapper, "权限不足: " + message);
        } else if (throwable.getClass().getSimpleName().contains("NotFound")) {
            return writeNotFoundResponse(response, objectMapper, requestPath);
        } else if (throwable.getClass().getSimpleName().contains("Timeout")) {
            return writeGatewayTimeoutResponse(response, objectMapper, "请求超时: " + message);
        } else {
            return writeInternalServerErrorResponse(response, objectMapper, "系统内部错误", null);
        }
    }

    /**
     * 创建标准的错误数据对象
     *
     * @param path      请求路径
     * @param requestId 请求ID
     * @return 错误数据对象
     */
    public static Map<String, Object> createErrorData(String path, String requestId) {
        Map<String, Object> data = new HashMap<>();
        if (path != null) {
            data.put("path", path);
        }
        if (requestId != null) {
            data.put("requestId", requestId);
        }
        data.put("timestamp", LocalDateTime.now());
        return data;
    }
}
