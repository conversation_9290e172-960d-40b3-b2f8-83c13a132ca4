package com.trinasolar.gateway.security.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.common.core.exception.AuthorizationException;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.gateway.security.config.ApiPathInit;
import com.trinasolar.gateway.security.sureness.Subject;
import com.trinasolar.gateway.security.sureness.matcher.TreePathRoleMatcher;
import com.trinasolar.gateway.security.sureness.util.SurenessCommonUtil;
import com.trinasolar.gateway.security.sureness.util.SurenessContextHolder;
import com.trinasolar.gateway.security.util.WebFluxUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.net.InetSocketAddress;
import java.util.List;

/**
 * SurenessFilter factory
 * <p>
 * after OmniRequestGlobalFilter processing
 *
 * <AUTHOR>
 * @since 2025-07-08 18:39
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SurenessGatewayFilterFactory extends AbstractGatewayFilterFactory<Object> implements Ordered {

    private final ObjectMapper objectMapper;

    @Override
    public GatewayFilter apply(Object config) {
        return (exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();
            ServerHttpResponse response = exchange.getResponse();
            try {
                Subject subject = checkIn(request, ApiPathInit.getInstance());
                // You can consider using surenessContextHolder to bind subject in threadLocal
                // if bind, please remove it when end
                if (subject != null) {
                    SurenessContextHolder.bindSubject(subject);
                }
            } catch (AuthorizationException e) {
                log.debug("this account can not access this resource");
                return WebFluxUtils.handleForbidden(objectMapper, response, e.getMessage());
            } catch (RuntimeException e) {
                log.error("other exception happen: ", e);
                return WebFluxUtils.handleForbidden(objectMapper, response, e.getMessage());
            }

            return chain.filter(exchange).doFinally(x -> SurenessContextHolder.unbindSubject());
        };
    }

    /**
     * 动态路由授权过滤器名称，可全局配置或者单个服务路由配置
     * spring:
     *   cloud:
     *     gateway:
     *       server:
     *         webflux:
     *           default-filters:
     *             # 令牌中继
     *             - TokenRelay=
     *             - ApiAuth
     *           routes:
     *             - id: omni-resource-example
     *               uri: lb://omni-resource-example
     *               predicates:
     *                 - Path=/example/**
     *               filters:
     *                 - ApiAuth
     *
     * @return {@link String }
     */
    @Override
    public String name() {
        return "ApiAuth";
    }

    /**
     * after OmniRequestGlobalFilter processing
     *
     * @return int
     */
    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 3;
    }

    /**
     * check and bind subject
     *
     * @param request         request
     * @param pathRoleMatcher pathRoleMatcher
     * @return {@link Subject }
     */
    private Subject checkIn(ServerHttpRequest request, TreePathRoleMatcher pathRoleMatcher) {
        String authorization = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
        if (StringUtils.hasText(authorization) && authorization.startsWith(SecurityConstant.TOKEN_PREFIX)) {
            String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length()).trim();
            String userAgent = request.getHeaders().getFirst(HttpHeaders.USER_AGENT);
            InetSocketAddress remoteAddress = request.getRemoteAddress();
            String remoteHost = remoteAddress == null ? "" : remoteAddress.getHostString();
            String requestUri = request.getPath().value();
            String requestType = request.getMethod().name();
            String targetUri = requestUri.concat("===").concat(requestType).toLowerCase();
            Subject subject = Subject.builder()
                    .userAgent(SurenessCommonUtil.findUserAgent(userAgent))
                    .remoteHost(remoteHost)
                    .targetUri(targetUri)
                    .token(token)
                    .build();

            if (pathRoleMatcher.isExcludedResource(subject)) {
                return null;
            }

            pathRoleMatcher.matchRole(subject);

            authorized(subject);
            return subject;
        } else {
            return null;
        }
    }

    /**
     * 授权
     *
     * @param subject 主题
     * @throws AuthorizationException oauth2授权异常
     */
    public void authorized(Subject subject) throws AuthorizationException {
        List<String> ownRoles = subject.getRoles();
        List<String> supportRoles = subject.getSupportRoles();
        // if null, note that not config this resource
        if (supportRoles == null) {
            return;
        }
        // if config, ownRole must contain the supportRole item
        if (ownRoles != null && supportRoles.stream().anyMatch(ownRoles::contains)) {
            return;
        }
        throw new AuthorizationException("您没有权限，拒绝访问", null);
    }
}
