package com.trinasolar.gateway.security.filter;


import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 全局跨域配置
 *<p>
 * 解决 CORS 头部重复设置问题：
 * 1. 动态根据请求的 Origin 设置响应头，避免使用通配符 *
 * 2. 先清除已存在的 CORS 头部，防止重复设置
 * 3. 支持通过配置文件灵活配置允许的域名
 * 4. 支持子域名匹配和本地开发环境
 *
 * <AUTHOR>
 * @since 2025-07-24 21:42
 **/
@Component
public class CorsHeaderFilter implements GlobalFilter, Ordered {

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        if (exchange.getRequest().getMethod() == HttpMethod.OPTIONS) {
            exchange.getResponse().setStatusCode(HttpStatus.OK);
            setCorsHeaders(exchange.getResponse().getHeaders());
            return exchange.getResponse().setComplete();
        }

        return chain.filter(exchange).then(Mono.fromRunnable(() -> {
            cleanDuplicateCorsHeaders(exchange.getResponse().getHeaders());
        }));
    }

    private void setCorsHeaders(HttpHeaders headers) {
        headers.set(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
        headers.set(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "*");
        headers.set(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*");
        headers.set(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
        headers.set(HttpHeaders.ACCESS_CONTROL_MAX_AGE, "3600");
        headers.set(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "*");
    }

    private void cleanDuplicateCorsHeaders(HttpHeaders headers) {
        headers.remove(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN);
        headers.remove(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS);
        headers.remove(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS);
        headers.remove(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS);
        headers.remove(HttpHeaders.ACCESS_CONTROL_MAX_AGE);
        headers.remove(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS);

        List<String> varyHeaders = headers.get(HttpHeaders.VARY);
        if (varyHeaders != null) {
            boolean shouldRemoveVary = varyHeaders.stream()
                    .anyMatch(vary -> vary.contains("Origin") ||
                            vary.contains("Access-Control-Request-Method") ||
                            vary.contains("Access-Control-Request-Headers"));
            if (shouldRemoveVary) {
                headers.remove(HttpHeaders.VARY);
            }
        }

        setCorsHeaders(headers);
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}