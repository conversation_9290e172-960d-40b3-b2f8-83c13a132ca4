package com.trinasolar.gateway.security.filter;


import com.trinasolar.gateway.security.properties.CorsProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 全局跨域配置
 *<p>
 * 解决 CORS 头部重复设置问题：
 * 1. 动态根据请求的 Origin 设置响应头，避免使用通配符 *
 * 2. 先清除已存在的 CORS 头部，防止重复设置
 * 3. 支持通过配置文件灵活配置允许的域名
 * 4. 支持子域名匹配和本地开发环境
 *
 * <AUTHOR>
 * @since 2025-07-24 21:42
 **/
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(CorsProperties.class)
public class CorsFilter implements WebFilter, Ordered {

    private final CorsProperties corsProperties;

    @Override
    @SuppressWarnings("all")
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        // 如果 CORS 过滤器被禁用，直接通过
        if (!corsProperties.isEnabled()) {
            return chain.filter(exchange);
        }
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        HttpHeaders headers = response.getHeaders();

        // 获取请求的 Origin
        String origin = request.getHeaders().getFirst(HttpHeaders.ORIGIN);

        // 先清除可能已存在的 CORS 头部，避免重复设置
        headers.remove(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN);
        headers.remove(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS);
        headers.remove(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS);
        headers.remove(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS);
        headers.remove(HttpHeaders.ACCESS_CONTROL_MAX_AGE);
        headers.remove(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS);
        headers.remove(HttpHeaders.VARY);

        // 设置 CORS 头部
        if (origin != null && isAllowedOrigin(origin)) {
            // 对于允许的域名，设置具体的 origin 而不是通配符
            headers.set(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, origin);
            if (corsProperties.isAllowCredentials()) {
                headers.set(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
            }
        } else if (origin == null) {
            // 对于非浏览器请求（如 Postman、服务间调用），设置为通配符
            headers.set(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
            // 注意：当设置为 * 时，不能同时设置 credentials 为 true
        } else {
            // 对于不在白名单中的域名，根据策略处理
            // 这里选择拒绝跨域请求，可以根据需要调整
            // 如果需要更宽松的策略，可以设置为 "*"
            //return handleForbiddenOrigin(response);
            return handleForbiddenOrigin(response);
        }

        // 设置其他 CORS 头部
        headers.set(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, corsProperties.getAllowedHeaders());
        headers.set(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, corsProperties.getAllowedMethods());
        headers.set(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, corsProperties.getExposedHeaders());
        headers.set(HttpHeaders.ACCESS_CONTROL_MAX_AGE, String.valueOf(corsProperties.getMaxAge()));

        // 处理预检请求
        if (request.getMethod() == HttpMethod.OPTIONS) {
            response.setStatusCode(HttpStatus.OK);
            return Mono.empty();
        }

        return chain.filter(exchange);
    }

    /**
     * 检查是否为允许的域名
     *
     * @param origin 请求的 origin
     * @return 是否允许
     */
    private boolean isAllowedOrigin(String origin) {
        if (origin == null || origin.isEmpty()) {
            return false;
        }

        List<String> allowedOrigins = corsProperties.getAllowedOrigins();

        // 精确匹配
        if (allowedOrigins.contains(origin)) {
            return true;
        }

        // 支持本地开发环境
        if (isLocalDevelopment(origin)) {
            return true;
        }

        // 支持子域名匹配
        if (corsProperties.isAllowSubdomains()) {
            return isAllowedSubdomain(origin);
        }

        return false;
    }

    /**
     * 检查是否为本地开发环境
     */
    private boolean isLocalDevelopment(String origin) {
        return origin.startsWith("http://localhost:") ||
                origin.startsWith("http://127.0.0.1:") ||
                origin.startsWith("https://localhost:") ||
                origin.startsWith("https://127.0.0.1:");
    }

    /**
     * 检查是否为允许的子域名
     */
    private boolean isAllowedSubdomain(String origin) {
        for (String domain : corsProperties.getAllowedDomains()) {
            if (origin.endsWith("." + domain)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }

    /**
     * 处理不允许的域名请求
     */
    private Mono<Void> handleForbiddenOrigin(ServerHttpResponse response) {
        response.setStatusCode(HttpStatus.FORBIDDEN);
        response.getHeaders().set(HttpHeaders.CONTENT_TYPE, "application/json;charset=UTF-8");
        String body = "{\"code\":1,\"msg\":\"CORS policy: Origin not allowed\"}";
        return response.writeWith(Mono.just(response.bufferFactory().wrap(body.getBytes())));
    }
}